#!/usr/bin/env python3

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demo_engine_components():
    print("🎮 3D Game Engine Demo")
    print("=" * 50)
    
    try:
        print("📦 Testing Core Engine Components...")
        
        from engine.entity import Entity
        from engine.components import Transform, MeshRenderer, Light
        from engine.scene import Scene
        
        scene = Scene("Demo Scene")
        
        player_entity = Entity("Player")
        player_transform = player_entity.get_component(Transform)
        player_transform.position = [0, 1, 0]
        player_entity.add_component(MeshRenderer())
        scene.add_entity(player_entity)
        
        light_entity = Entity("Main Light")
        light_transform = light_entity.get_component(Transform)
        light_transform.rotation = [45, 0, 0]
        light_entity.add_component(Light())
        scene.add_entity(light_entity)
        
        print(f"✅ Scene created with {len(scene.entities)} entities")
        print(f"   - Player at position {player_transform.position}")
        print(f"   - Light at rotation {light_transform.rotation}")
        
    except Exception as e:
        print(f"❌ Engine test failed: {e}")
        return False
    
    try:
        print("\n🎨 Testing Editor Components...")
        
        from editor.project_manager import ProjectManager
        from editor.game_builder import GameBuilder
        
        pm = ProjectManager()
        gb = GameBuilder()
        
        print("✅ Project Manager initialized")
        print("✅ Game Builder initialized")
        
    except Exception as e:
        print(f"❌ Editor test failed: {e}")
        return False
    
    try:
        print("\n🎯 Testing Player and Server...")
        
        from player.game_player import GamePlayer
        from server.game_server import GameServer
        
        print("✅ Game Player class available")
        print("✅ Game Server class available")
        
    except Exception as e:
        print(f"❌ Player/Server test failed: {e}")
        return False
    
    return True

def show_features():
    print("\n🚀 Available Features:")
    print("=" * 50)
    
    features = [
        ("✅ Core Engine", "GameEngine, Scene Management, ECS"),
        ("✅ 3D Rendering", "OpenGL pipeline with ModernGL"),
        ("✅ Asset Management", "Textures, models, sounds, materials"),
        ("✅ Visual Editor", "PyQt6 interface with dockable panels"),
        ("✅ Project System", "Project creation and configuration"),
        ("✅ Game Export", "Zip packaging for games and servers"),
        ("✅ Game Player", "Standalone runtime for exported games"),
        ("✅ Server System", "Multiplayer dedicated server"),
        ("🚧 Physics", "PyBullet integration (framework ready)"),
        ("🚧 Multiplayer", "Real-time synchronization (basic framework)"),
        ("🚧 Map Editor", "Terrain and world editing tools"),
        ("🚧 NPC System", "AI entities and behaviors"),
    ]
    
    for status, feature in features:
        print(f"  {status} {feature}")

def show_usage():
    print("\n📖 Usage Instructions:")
    print("=" * 50)
    
    print("1. Install dependencies:")
    print("   pip install -r requirements.txt")
    print()
    print("2. Launch the visual editor:")
    print("   python main.py")
    print()
    print("3. Create a new project in the editor")
    print("4. Add entities and components")
    print("5. Build and export your game")
    print("6. Run exported games:")
    print("   python player/game_player.py game.zip")
    print()
    print("7. Run multiplayer server:")
    print("   python server/game_server.py server.zip")

def show_architecture():
    print("\n🏗️  Architecture Overview:")
    print("=" * 50)
    
    print("Entity-Component System:")
    print("  • Entities: Game objects with unique IDs")
    print("  • Components: Data containers (Transform, MeshRenderer, etc.)")
    print("  • Systems: Logic processors (rendering, physics, etc.)")
    print()
    print("Rendering Pipeline:")
    print("  • OpenGL 3.3 Core Profile with ModernGL")
    print("  • Shader-based material system")
    print("  • Forward rendering with lighting")
    print("  • Camera-based view transformations")
    print()
    print("Editor Architecture:")
    print("  • PyQt6 dockable interface")
    print("  • Real-time scene editing")
    print("  • Asset management and importing")
    print("  • Project configuration and building")

if __name__ == "__main__":
    success = demo_engine_components()
    
    if success:
        print("\n🎉 All core components working!")
        show_features()
        show_usage()
        show_architecture()
        
        print("\n" + "=" * 50)
        print("🎮 3D Game Engine is ready for development!")
        print("   Launch the editor with: python main.py")
        print("=" * 50)
    else:
        print("\n❌ Some components failed. Check dependencies.")
        print("   Install with: pip install -r requirements.txt")
