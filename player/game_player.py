#!/usr/bin/env python3

import sys
import os
import json
import zipfile
import tempfile
import shutil
import pygame

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from engine import GameEngine
from engine.scene import Scene
from engine.camera import FPSCamera, OrbitCamera
from engine.entity import Entity
from engine.components import *

class GamePlayer:
    def __init__(self, game_package_path: str = None):
        self.game_package_path = game_package_path
        self.game_config = {}
        self.temp_dir = None
        self.engine = None
        self.player_entity = None
        self.current_camera = None
        
        if game_package_path:
            self.load_game_package()
    
    def load_game_package(self):
        if not os.path.exists(self.game_package_path):
            print(f"Game package not found: {self.game_package_path}")
            return False
        
        try:
            self.temp_dir = tempfile.mkdtemp()
            
            with zipfile.ZipFile(self.game_package_path, 'r') as zip_ref:
                zip_ref.extractall(self.temp_dir)
            
            config_path = os.path.join(self.temp_dir, "game_config.json")
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    self.game_config = json.load(f)
            
            return True
            
        except Exception as e:
            print(f"Failed to load game package: {e}")
            return False
    
    def run(self):
        try:
            if not self.game_config:
                print("No game configuration found")
                return
            
            player_settings = self.game_config.get("player_settings", {})
            resolution = player_settings.get("default_resolution", [1280, 720])
            fullscreen = player_settings.get("fullscreen", False)
            title = self.game_config.get("name", "Game")
            
            self.engine = GameEngine(resolution[0], resolution[1], title)
            
            if fullscreen:
                pygame.display.set_mode(resolution, pygame.FULLSCREEN | pygame.OPENGL | pygame.DOUBLEBUF)
            
            self.load_game_scenes()
            self.setup_player()
            self.setup_input_handling()
            
            self.engine.run()
            
        except Exception as e:
            print(f"Error running game: {e}")
        finally:
            self.cleanup()
    
    def load_game_scenes(self):
        if not self.temp_dir:
            return
        
        scenes_dir = os.path.join(self.temp_dir, "Scenes")
        if not os.path.exists(scenes_dir):
            return
        
        default_scene_name = self.game_config.get("default_scene", "main_scene")
        scene_file = os.path.join(scenes_dir, f"{default_scene_name}.json")
        
        if os.path.exists(scene_file):
            with open(scene_file, 'r') as f:
                scene_data = json.load(f)
            
            scene = Scene.from_dict(scene_data)
            scene_manager = self.engine.get_scene_manager()
            scene_manager.add_scene(scene)
            scene_manager.set_current_scene(scene.id)
    
    def setup_player(self):
        scene = self.engine.get_scene_manager().get_current_scene()
        if not scene:
            return
        
        spawn_points = scene.get_entities_by_tag("spawn_point")
        
        if spawn_points:
            spawn_point = spawn_points[0]
            spawn_transform = spawn_point.get_component(Transform)
            spawn_position = spawn_transform.position if spawn_transform else [0, 0, 0]
        else:
            spawn_position = [0, 1, 0]
        
        self.player_entity = Entity("Player")
        player_transform = self.player_entity.get_component(Transform)
        player_transform.position = spawn_position
        
        scene.add_entity(self.player_entity)
        
        self.current_camera = FPSCamera()
        self.current_camera.set_position(spawn_position)
        self.engine.get_renderer().set_camera(self.current_camera)
    
    def setup_input_handling(self):
        input_manager = self.engine.get_input_manager()
        
        def handle_input():
            dt = self.engine.get_time_manager().get_delta_time()
            
            if input_manager.is_key_pressed(pygame.K_w):
                self.current_camera.move_forward(5.0 * dt)
            if input_manager.is_key_pressed(pygame.K_s):
                self.current_camera.move_backward(5.0 * dt)
            if input_manager.is_key_pressed(pygame.K_a):
                self.current_camera.move_left(5.0 * dt)
            if input_manager.is_key_pressed(pygame.K_d):
                self.current_camera.move_right(5.0 * dt)
            if input_manager.is_key_pressed(pygame.K_SPACE):
                self.current_camera.move_up(5.0 * dt)
            if input_manager.is_key_pressed(pygame.K_LSHIFT):
                self.current_camera.move_down(5.0 * dt)
            
            if input_manager.is_key_just_pressed(pygame.K_ESCAPE):
                if input_manager.mouse_locked:
                    input_manager.unlock_mouse()
                else:
                    self.engine.shutdown()
            
            if input_manager.is_mouse_button_just_pressed(1):
                if not input_manager.mouse_locked:
                    input_manager.lock_mouse()
            
            if input_manager.mouse_locked:
                mouse_delta = input_manager.get_mouse_delta()
                self.current_camera.rotate(-mouse_delta[0], -mouse_delta[1])
            
            if self.player_entity:
                player_transform = self.player_entity.get_component(Transform)
                if player_transform:
                    player_transform.position = self.current_camera.position.copy()
        
        original_update = self.engine._update
        def new_update(dt):
            original_update(dt)
            handle_input()
        
        self.engine._update = new_update
    
    def cleanup(self):
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

def main():
    if len(sys.argv) < 2:
        print("Usage: python game_player.py <game_package.zip>")
        sys.exit(1)
    
    game_package = sys.argv[1]
    player = GamePlayer(game_package)
    player.run()

if __name__ == "__main__":
    main()
