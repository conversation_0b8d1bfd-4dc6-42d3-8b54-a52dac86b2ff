from typing import Optional, Dict, Any, List
import numpy as np

from .entity import Component
from .renderer import Mesh, Material

class MeshRenderer(Component):
    def __init__(self, mesh: Optional[Mesh] = None, material: Optional[Material] = None):
        super().__init__()
        self.mesh = mesh
        self.material = material or Material()
        self.cast_shadows = True
        self.receive_shadows = True
    
    def set_mesh(self, mesh: Mesh):
        self.mesh = mesh
    
    def set_material(self, material: Material):
        self.material = material
    
    def to_dict(self) -> Dict[str, Any]:
        data = super().to_dict()
        data.update({
            'cast_shadows': self.cast_shadows,
            'receive_shadows': self.receive_shadows
        })
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MeshRenderer':
        renderer = cls()
        renderer.enabled = data.get('enabled', True)
        renderer.cast_shadows = data.get('cast_shadows', True)
        renderer.receive_shadows = data.get('receive_shadows', True)
        return renderer

class Light(Component):
    def __init__(self, light_type: str = "directional"):
        super().__init__()
        self.light_type = light_type
        self.color = [1.0, 1.0, 1.0]
        self.intensity = 1.0
        self.range = 10.0
        self.spot_angle = 30.0
        self.cast_shadows = True
    
    def to_dict(self) -> Dict[str, Any]:
        data = super().to_dict()
        data.update({
            'light_type': self.light_type,
            'color': self.color,
            'intensity': self.intensity,
            'range': self.range,
            'spot_angle': self.spot_angle,
            'cast_shadows': self.cast_shadows
        })
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Light':
        light = cls(data.get('light_type', 'directional'))
        light.enabled = data.get('enabled', True)
        light.color = data.get('color', [1.0, 1.0, 1.0])
        light.intensity = data.get('intensity', 1.0)
        light.range = data.get('range', 10.0)
        light.spot_angle = data.get('spot_angle', 30.0)
        light.cast_shadows = data.get('cast_shadows', True)
        return light

class Rigidbody(Component):
    def __init__(self, mass: float = 1.0):
        super().__init__()
        self.mass = mass
        self.velocity = [0.0, 0.0, 0.0]
        self.angular_velocity = [0.0, 0.0, 0.0]
        self.use_gravity = True
        self.is_kinematic = False
        self.freeze_rotation = [False, False, False]
        self.freeze_position = [False, False, False]
        self.drag = 0.0
        self.angular_drag = 0.05
        
        self.physics_body = None
    
    def add_force(self, force: List[float]):
        if not self.is_kinematic and self.physics_body:
            pass
    
    def add_impulse(self, impulse: List[float]):
        if not self.is_kinematic and self.physics_body:
            pass
    
    def to_dict(self) -> Dict[str, Any]:
        data = super().to_dict()
        data.update({
            'mass': self.mass,
            'velocity': self.velocity,
            'angular_velocity': self.angular_velocity,
            'use_gravity': self.use_gravity,
            'is_kinematic': self.is_kinematic,
            'freeze_rotation': self.freeze_rotation,
            'freeze_position': self.freeze_position,
            'drag': self.drag,
            'angular_drag': self.angular_drag
        })
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Rigidbody':
        rigidbody = cls(data.get('mass', 1.0))
        rigidbody.enabled = data.get('enabled', True)
        rigidbody.velocity = data.get('velocity', [0.0, 0.0, 0.0])
        rigidbody.angular_velocity = data.get('angular_velocity', [0.0, 0.0, 0.0])
        rigidbody.use_gravity = data.get('use_gravity', True)
        rigidbody.is_kinematic = data.get('is_kinematic', False)
        rigidbody.freeze_rotation = data.get('freeze_rotation', [False, False, False])
        rigidbody.freeze_position = data.get('freeze_position', [False, False, False])
        rigidbody.drag = data.get('drag', 0.0)
        rigidbody.angular_drag = data.get('angular_drag', 0.05)
        return rigidbody

class Collider(Component):
    def __init__(self, collider_type: str = "box"):
        super().__init__()
        self.collider_type = collider_type
        self.is_trigger = False
        self.size = [1.0, 1.0, 1.0]
        self.radius = 0.5
        self.height = 2.0
        self.center = [0.0, 0.0, 0.0]
        
        self.physics_shape = None
    
    def to_dict(self) -> Dict[str, Any]:
        data = super().to_dict()
        data.update({
            'collider_type': self.collider_type,
            'is_trigger': self.is_trigger,
            'size': self.size,
            'radius': self.radius,
            'height': self.height,
            'center': self.center
        })
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Collider':
        collider = cls(data.get('collider_type', 'box'))
        collider.enabled = data.get('enabled', True)
        collider.is_trigger = data.get('is_trigger', False)
        collider.size = data.get('size', [1.0, 1.0, 1.0])
        collider.radius = data.get('radius', 0.5)
        collider.height = data.get('height', 2.0)
        collider.center = data.get('center', [0.0, 0.0, 0.0])
        return collider

class AudioSource(Component):
    def __init__(self):
        super().__init__()
        self.audio_clip = None
        self.volume = 1.0
        self.pitch = 1.0
        self.loop = False
        self.play_on_awake = False
        self.spatial_blend = 1.0
        self.min_distance = 1.0
        self.max_distance = 500.0
        
        self.is_playing = False
        self.is_paused = False
    
    def play(self):
        if self.audio_clip:
            self.is_playing = True
            self.is_paused = False
    
    def stop(self):
        self.is_playing = False
        self.is_paused = False
    
    def pause(self):
        if self.is_playing:
            self.is_paused = True
    
    def resume(self):
        if self.is_paused:
            self.is_paused = False
    
    def to_dict(self) -> Dict[str, Any]:
        data = super().to_dict()
        data.update({
            'volume': self.volume,
            'pitch': self.pitch,
            'loop': self.loop,
            'play_on_awake': self.play_on_awake,
            'spatial_blend': self.spatial_blend,
            'min_distance': self.min_distance,
            'max_distance': self.max_distance
        })
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AudioSource':
        audio_source = cls()
        audio_source.enabled = data.get('enabled', True)
        audio_source.volume = data.get('volume', 1.0)
        audio_source.pitch = data.get('pitch', 1.0)
        audio_source.loop = data.get('loop', False)
        audio_source.play_on_awake = data.get('play_on_awake', False)
        audio_source.spatial_blend = data.get('spatial_blend', 1.0)
        audio_source.min_distance = data.get('min_distance', 1.0)
        audio_source.max_distance = data.get('max_distance', 500.0)
        return audio_source

class Script(Component):
    def __init__(self, script_name: str = ""):
        super().__init__()
        self.script_name = script_name
        self.variables: Dict[str, Any] = {}
    
    def to_dict(self) -> Dict[str, Any]:
        data = super().to_dict()
        data.update({
            'script_name': self.script_name,
            'variables': self.variables
        })
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Script':
        script = cls(data.get('script_name', ''))
        script.enabled = data.get('enabled', True)
        script.variables = data.get('variables', {})
        return script
