import moderngl
import numpy as np
from typing import Optional, List, Dict, Any
import pyrr

from .camera import Camera
from .entity import Entity
from .scene import Scene

class Shader:
    def __init__(self, ctx: moderngl.Context, vertex_source: str, fragment_source: str):
        self.ctx = ctx
        self.program = ctx.program(vertex_shader=vertex_source, fragment_shader=fragment_source)
        self.uniforms = {}
    
    def use(self):
        self.program.use()
    
    def set_uniform(self, name: str, value):
        if name in self.program:
            self.program[name].value = value
    
    def set_matrix4(self, name: str, matrix: np.ndarray):
        if name in self.program:
            self.program[name].write(matrix.astype(np.float32).tobytes())

class Mesh:
    def __init__(self, ctx: moderngl.Context, vertices: np.ndarray, indices: np.ndarray = None, 
                 normals: np.ndarray = None, uvs: np.ndarray = None):
        self.ctx = ctx
        self.vertices = vertices
        self.indices = indices
        self.normals = normals
        self.uvs = uvs
        
        self.vao = None
        self.vbo = None
        self.ibo = None
        
        self._setup_buffers()
    
    def _setup_buffers(self):
        vertex_data = self.vertices
        
        if self.normals is not None:
            vertex_data = np.hstack([vertex_data, self.normals])
        
        if self.uvs is not None:
            vertex_data = np.hstack([vertex_data, self.uvs])
        
        self.vbo = self.ctx.buffer(vertex_data.astype(np.float32).tobytes())
        
        if self.indices is not None:
            self.ibo = self.ctx.buffer(self.indices.astype(np.uint32).tobytes())
        
        vertex_size = 3
        if self.normals is not None:
            vertex_size += 3
        if self.uvs is not None:
            vertex_size += 2
        
        vertex_format = [(self.vbo, '3f', 'in_position')]
        offset = 3
        
        if self.normals is not None:
            vertex_format.append((self.vbo, f'{offset}f 3f', 'in_normal'))
            offset += 3
        
        if self.uvs is not None:
            vertex_format.append((self.vbo, f'{offset}f 2f', 'in_uv'))
        
        if self.ibo:
            self.vao = self.ctx.vertex_array(self.ctx.program(), vertex_format, self.ibo)
        else:
            self.vao = self.ctx.vertex_array(self.ctx.program(), vertex_format)
    
    def render(self):
        if self.vao:
            self.vao.render()

class Material:
    def __init__(self):
        self.diffuse_color = [1.0, 1.0, 1.0, 1.0]
        self.specular_color = [1.0, 1.0, 1.0]
        self.shininess = 32.0
        self.diffuse_texture = None
        self.normal_texture = None
        self.specular_texture = None

class Renderer:
    def __init__(self, ctx: moderngl.Context, width: int, height: int):
        self.ctx = ctx
        self.width = width
        self.height = height
        
        self.default_shader = self._create_default_shader()
        self.current_camera: Optional[Camera] = None
        
        self.view_matrix = np.eye(4, dtype=np.float32)
        self.projection_matrix = np.eye(4, dtype=np.float32)
        
        self._setup_default_camera()
    
    def _create_default_shader(self) -> Shader:
        vertex_shader = """
        #version 330 core
        
        in vec3 in_position;
        in vec3 in_normal;
        in vec2 in_uv;
        
        uniform mat4 model;
        uniform mat4 view;
        uniform mat4 projection;
        uniform mat3 normal_matrix;
        
        out vec3 frag_pos;
        out vec3 normal;
        out vec2 uv;
        
        void main() {
            vec4 world_pos = model * vec4(in_position, 1.0);
            frag_pos = world_pos.xyz;
            normal = normal_matrix * in_normal;
            uv = in_uv;
            
            gl_Position = projection * view * world_pos;
        }
        """
        
        fragment_shader = """
        #version 330 core
        
        in vec3 frag_pos;
        in vec3 normal;
        in vec2 uv;
        
        uniform vec3 camera_pos;
        uniform vec4 diffuse_color;
        uniform vec3 specular_color;
        uniform float shininess;
        
        uniform vec3 light_direction;
        uniform vec3 light_color;
        uniform vec3 ambient_light;
        
        out vec4 frag_color;
        
        void main() {
            vec3 norm = normalize(normal);
            vec3 light_dir = normalize(-light_direction);
            
            float diff = max(dot(norm, light_dir), 0.0);
            vec3 diffuse = diff * light_color;
            
            vec3 view_dir = normalize(camera_pos - frag_pos);
            vec3 reflect_dir = reflect(-light_dir, norm);
            float spec = pow(max(dot(view_dir, reflect_dir), 0.0), shininess);
            vec3 specular = spec * specular_color * light_color;
            
            vec3 result = (ambient_light + diffuse + specular) * diffuse_color.rgb;
            frag_color = vec4(result, diffuse_color.a);
        }
        """
        
        return Shader(self.ctx, vertex_shader, fragment_shader)
    
    def _setup_default_camera(self):
        self.current_camera = Camera()
        self.current_camera.position = [0.0, 0.0, 5.0]
        self.update_projection_matrix()
    
    def set_camera(self, camera: Camera):
        self.current_camera = camera
        self.update_projection_matrix()
    
    def update_projection_matrix(self):
        if self.current_camera:
            aspect_ratio = self.width / self.height
            self.projection_matrix = pyrr.matrix44.create_perspective_projection(
                self.current_camera.fov, aspect_ratio, 
                self.current_camera.near_plane, self.current_camera.far_plane
            )
    
    def update_view_matrix(self):
        if self.current_camera:
            self.view_matrix = self.current_camera.get_view_matrix()
    
    def render_scene(self, scene: Scene):
        if not self.current_camera:
            return
        
        self.update_view_matrix()
        
        self.default_shader.use()
        self.default_shader.set_matrix4('view', self.view_matrix)
        self.default_shader.set_matrix4('projection', self.projection_matrix)
        
        self.default_shader.set_uniform('camera_pos', self.current_camera.position)
        self.default_shader.set_uniform('light_direction', scene.directional_light['direction'])
        self.default_shader.set_uniform('light_color', scene.directional_light['color'])
        self.default_shader.set_uniform('ambient_light', scene.ambient_light)
        
        for entity in scene.entities.values():
            if entity.active:
                self.render_entity(entity)
    
    def render_entity(self, entity: Entity):
        from .components import MeshRenderer
        from .entity import Transform
        
        transform = entity.get_component(Transform)
        mesh_renderer = entity.get_component(MeshRenderer)
        
        if not transform or not mesh_renderer:
            return
        
        model_matrix = self._create_model_matrix(transform)
        normal_matrix = np.linalg.inv(model_matrix[:3, :3]).T
        
        self.default_shader.set_matrix4('model', model_matrix)
        self.default_shader.set_uniform('normal_matrix', normal_matrix)
        
        if mesh_renderer.material:
            material = mesh_renderer.material
            self.default_shader.set_uniform('diffuse_color', material.diffuse_color)
            self.default_shader.set_uniform('specular_color', material.specular_color)
            self.default_shader.set_uniform('shininess', material.shininess)
        else:
            self.default_shader.set_uniform('diffuse_color', [1.0, 1.0, 1.0, 1.0])
            self.default_shader.set_uniform('specular_color', [1.0, 1.0, 1.0])
            self.default_shader.set_uniform('shininess', 32.0)
        
        if mesh_renderer.mesh:
            mesh_renderer.mesh.render()
    
    def _create_model_matrix(self, transform) -> np.ndarray:
        translation = pyrr.matrix44.create_translation(transform.position)
        
        rotation_x = pyrr.matrix44.create_from_x_rotation(np.radians(transform.rotation[0]))
        rotation_y = pyrr.matrix44.create_from_y_rotation(np.radians(transform.rotation[1]))
        rotation_z = pyrr.matrix44.create_from_z_rotation(np.radians(transform.rotation[2]))
        rotation = pyrr.matrix44.multiply(rotation_z, pyrr.matrix44.multiply(rotation_y, rotation_x))
        
        scale = pyrr.matrix44.create_scale(transform.scale)
        
        model_matrix = pyrr.matrix44.multiply(translation, pyrr.matrix44.multiply(rotation, scale))
        return model_matrix
    
    def resize(self, width: int, height: int):
        self.width = width
        self.height = height
        self.ctx.viewport = (0, 0, width, height)
        self.update_projection_matrix()
