import os
import json
from typing import Dict, Any, Optional, List
from PIL import Image
import numpy as np
import trimesh
import pygame

class Asset:
    def __init__(self, name: str, asset_type: str, file_path: str):
        self.name = name
        self.type = asset_type
        self.file_path = file_path
        self.data: Any = None
        self.loaded = False
        self.metadata: Dict[str, Any] = {}
    
    def load(self):
        if self.loaded:
            return self.data
        
        try:
            if self.type == "texture":
                self.data = self._load_texture()
            elif self.type == "model":
                self.data = self._load_model()
            elif self.type == "sound":
                self.data = self._load_sound()
            elif self.type == "shader":
                self.data = self._load_shader()
            elif self.type == "material":
                self.data = self._load_material()
            
            self.loaded = True
            return self.data
        except Exception as e:
            print(f"Failed to load asset {self.name}: {e}")
            return None
    
    def _load_texture(self):
        img = Image.open(self.file_path)
        img = img.convert('RGBA')
        img_data = np.array(img)
        return {
            'data': img_data,
            'width': img.width,
            'height': img.height,
            'format': 'RGBA'
        }
    
    def _load_model(self):
        mesh = trimesh.load(self.file_path)
        if hasattr(mesh, 'vertices'):
            return {
                'vertices': mesh.vertices.astype(np.float32),
                'faces': mesh.faces.astype(np.uint32),
                'normals': mesh.vertex_normals.astype(np.float32) if hasattr(mesh, 'vertex_normals') else None,
                'uvs': mesh.visual.uv if hasattr(mesh.visual, 'uv') else None
            }
        return None
    
    def _load_sound(self):
        return pygame.mixer.Sound(self.file_path)
    
    def _load_shader(self):
        with open(self.file_path, 'r') as f:
            return f.read()
    
    def _load_material(self):
        with open(self.file_path, 'r') as f:
            return json.load(f)
    
    def unload(self):
        self.data = None
        self.loaded = False

class AssetManager:
    def __init__(self):
        self.assets: Dict[str, Asset] = {}
        self.asset_paths: Dict[str, str] = {}
        self.default_assets_path = "assets"
        
        self._create_default_assets()
    
    def _create_default_assets(self):
        os.makedirs(self.default_assets_path, exist_ok=True)
        os.makedirs(os.path.join(self.default_assets_path, "textures"), exist_ok=True)
        os.makedirs(os.path.join(self.default_assets_path, "models"), exist_ok=True)
        os.makedirs(os.path.join(self.default_assets_path, "sounds"), exist_ok=True)
        os.makedirs(os.path.join(self.default_assets_path, "shaders"), exist_ok=True)
        os.makedirs(os.path.join(self.default_assets_path, "materials"), exist_ok=True)
        
        self._create_default_texture()
        self._create_default_material()
    
    def _create_default_texture(self):
        default_texture_path = os.path.join(self.default_assets_path, "textures", "default.png")
        if not os.path.exists(default_texture_path):
            img = Image.new('RGBA', (64, 64), (255, 255, 255, 255))
            img.save(default_texture_path)
        
        self.register_asset("default_texture", "texture", default_texture_path)
    
    def _create_default_material(self):
        default_material_path = os.path.join(self.default_assets_path, "materials", "default.json")
        if not os.path.exists(default_material_path):
            material_data = {
                "name": "Default Material",
                "diffuse_color": [1.0, 1.0, 1.0, 1.0],
                "specular_color": [1.0, 1.0, 1.0],
                "shininess": 32.0,
                "diffuse_texture": "default_texture"
            }
            with open(default_material_path, 'w') as f:
                json.dump(material_data, f, indent=2)
        
        self.register_asset("default_material", "material", default_material_path)
    
    def register_asset(self, name: str, asset_type: str, file_path: str) -> bool:
        if not os.path.exists(file_path):
            print(f"Asset file not found: {file_path}")
            return False
        
        asset = Asset(name, asset_type, file_path)
        self.assets[name] = asset
        self.asset_paths[name] = file_path
        return True
    
    def load_asset(self, name: str) -> Optional[Any]:
        if name in self.assets:
            return self.assets[name].load()
        return None
    
    def get_asset(self, name: str) -> Optional[Asset]:
        return self.assets.get(name)
    
    def unload_asset(self, name: str):
        if name in self.assets:
            self.assets[name].unload()
    
    def unload_all_assets(self):
        for asset in self.assets.values():
            asset.unload()
    
    def scan_directory(self, directory: str, recursive: bool = True):
        if not os.path.exists(directory):
            return
        
        for root, dirs, files in os.walk(directory):
            for file in files:
                file_path = os.path.join(root, file)
                file_ext = os.path.splitext(file)[1].lower()
                
                asset_type = self._get_asset_type_from_extension(file_ext)
                if asset_type:
                    asset_name = os.path.splitext(file)[0]
                    relative_path = os.path.relpath(file_path, directory)
                    unique_name = relative_path.replace(os.sep, '_').replace('.', '_')
                    
                    self.register_asset(unique_name, asset_type, file_path)
            
            if not recursive:
                break
    
    def _get_asset_type_from_extension(self, ext: str) -> Optional[str]:
        texture_exts = ['.png', '.jpg', '.jpeg', '.bmp', '.tga', '.dds']
        model_exts = ['.obj', '.fbx', '.dae', '.gltf', '.glb', '.ply', '.stl']
        sound_exts = ['.wav', '.mp3', '.ogg', '.flac']
        shader_exts = ['.glsl', '.vert', '.frag', '.geom']
        material_exts = ['.json', '.mat']
        
        if ext in texture_exts:
            return "texture"
        elif ext in model_exts:
            return "model"
        elif ext in sound_exts:
            return "sound"
        elif ext in shader_exts:
            return "shader"
        elif ext in material_exts:
            return "material"
        
        return None
    
    def get_assets_by_type(self, asset_type: str) -> List[Asset]:
        return [asset for asset in self.assets.values() if asset.type == asset_type]
    
    def get_asset_list(self) -> List[str]:
        return list(self.assets.keys())
    
    def import_asset(self, source_path: str, name: str = None, target_dir: str = None) -> bool:
        if not os.path.exists(source_path):
            return False
        
        if not name:
            name = os.path.splitext(os.path.basename(source_path))[0]
        
        file_ext = os.path.splitext(source_path)[1].lower()
        asset_type = self._get_asset_type_from_extension(file_ext)
        
        if not asset_type:
            return False
        
        if not target_dir:
            type_dirs = {
                "texture": "textures",
                "model": "models", 
                "sound": "sounds",
                "shader": "shaders",
                "material": "materials"
            }
            target_dir = os.path.join(self.default_assets_path, type_dirs[asset_type])
        
        os.makedirs(target_dir, exist_ok=True)
        target_path = os.path.join(target_dir, os.path.basename(source_path))
        
        import shutil
        shutil.copy2(source_path, target_path)
        
        return self.register_asset(name, asset_type, target_path)
