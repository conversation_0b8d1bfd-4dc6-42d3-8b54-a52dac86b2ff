import pygame
from typing import Dict, Set, Tuple, Optional

class InputManager:
    def __init__(self):
        self.keys_pressed: Set[int] = set()
        self.keys_just_pressed: Set[int] = set()
        self.keys_just_released: Set[int] = set()
        
        self.mouse_buttons_pressed: Set[int] = set()
        self.mouse_buttons_just_pressed: Set[int] = set()
        self.mouse_buttons_just_released: Set[int] = set()
        
        self.mouse_position: Tuple[int, int] = (0, 0)
        self.mouse_delta: Tuple[int, int] = (0, 0)
        self.mouse_wheel_delta: int = 0
        
        self._last_mouse_position: Tuple[int, int] = (0, 0)
        
        pygame.mouse.set_visible(True)
        self.mouse_locked = False
    
    def handle_event(self, event):
        if event.type == pygame.KEYDOWN:
            self.keys_just_pressed.add(event.key)
            self.keys_pressed.add(event.key)
        elif event.type == pygame.KEYUP:
            self.keys_just_released.add(event.key)
            self.keys_pressed.discard(event.key)
        elif event.type == pygame.MOUSEBUTTONDOWN:
            self.mouse_buttons_just_pressed.add(event.button)
            self.mouse_buttons_pressed.add(event.button)
        elif event.type == pygame.MOUSEBUTTONUP:
            self.mouse_buttons_just_released.add(event.button)
            self.mouse_buttons_pressed.discard(event.button)
        elif event.type == pygame.MOUSEWHEEL:
            self.mouse_wheel_delta = event.y
    
    def update(self):
        self.keys_just_pressed.clear()
        self.keys_just_released.clear()
        self.mouse_buttons_just_pressed.clear()
        self.mouse_buttons_just_released.clear()
        
        self._last_mouse_position = self.mouse_position
        self.mouse_position = pygame.mouse.get_pos()
        
        if self.mouse_locked:
            center = (pygame.display.get_surface().get_width() // 2,
                     pygame.display.get_surface().get_height() // 2)
            self.mouse_delta = (
                self.mouse_position[0] - center[0],
                self.mouse_position[1] - center[1]
            )
            pygame.mouse.set_pos(center)
        else:
            self.mouse_delta = (
                self.mouse_position[0] - self._last_mouse_position[0],
                self.mouse_position[1] - self._last_mouse_position[1]
            )
        
        self.mouse_wheel_delta = 0
    
    def is_key_pressed(self, key: int) -> bool:
        return key in self.keys_pressed
    
    def is_key_just_pressed(self, key: int) -> bool:
        return key in self.keys_just_pressed
    
    def is_key_just_released(self, key: int) -> bool:
        return key in self.keys_just_released
    
    def is_mouse_button_pressed(self, button: int) -> bool:
        return button in self.mouse_buttons_pressed
    
    def is_mouse_button_just_pressed(self, button: int) -> bool:
        return button in self.mouse_buttons_just_pressed
    
    def is_mouse_button_just_released(self, button: int) -> bool:
        return button in self.mouse_buttons_just_released
    
    def get_mouse_position(self) -> Tuple[int, int]:
        return self.mouse_position
    
    def get_mouse_delta(self) -> Tuple[int, int]:
        return self.mouse_delta
    
    def get_mouse_wheel_delta(self) -> int:
        return self.mouse_wheel_delta
    
    def lock_mouse(self):
        self.mouse_locked = True
        pygame.mouse.set_visible(False)
        center = (pygame.display.get_surface().get_width() // 2,
                 pygame.display.get_surface().get_height() // 2)
        pygame.mouse.set_pos(center)
    
    def unlock_mouse(self):
        self.mouse_locked = False
        pygame.mouse.set_visible(True)
