import pygame
import moderngl
import numpy as np
from typing import Optional, Dict, Any
import sys
import os

from .scene import Scene<PERSON>anager
from .renderer import Renderer
from .input_manager import InputManager
from .time_manager import TimeManager
from .asset_manager import AssetManager

class GameEngine:
    def __init__(self, width: int = 1280, height: int = 720, title: str = "3D Game Engine"):
        self.width = width
        self.height = height
        self.title = title
        self.running = False
        
        self.window = None
        self.ctx = None
        
        self.scene_manager = SceneManager()
        self.renderer = None
        self.input_manager = None
        self.time_manager = TimeManager()
        self.asset_manager = AssetManager()
        
        self._initialize()
    
    def _initialize(self):
        pygame.init()
        
        pygame.display.gl_set_attribute(pygame.GL_CONTEXT_MAJOR_VERSION, 3)
        pygame.display.gl_set_attribute(pygame.GL_CONTEXT_MINOR_VERSION, 3)
        pygame.display.gl_set_attribute(pygame.GL_CONTEXT_PROFILE_MASK, pygame.GL_CONTEXT_PROFILE_CORE)
        pygame.display.gl_set_attribute(pygame.GL_DOUBLEBUFFER, 1)
        pygame.display.gl_set_attribute(pygame.GL_DEPTH_SIZE, 24)
        
        self.window = pygame.display.set_mode((self.width, self.height), pygame.OPENGL | pygame.DOUBLEBUF)
        pygame.display.set_caption(self.title)
        
        self.ctx = moderngl.create_context()
        self.ctx.enable(moderngl.DEPTH_TEST)
        self.ctx.enable(moderngl.CULL_FACE)
        
        self.renderer = Renderer(self.ctx, self.width, self.height)
        self.input_manager = InputManager()
        
        print(f"OpenGL Version: {self.ctx.info['GL_VERSION']}")
        print(f"OpenGL Renderer: {self.ctx.info['GL_RENDERER']}")
    
    def run(self):
        self.running = True
        clock = pygame.time.Clock()
        
        while self.running:
            dt = clock.tick(60) / 1000.0
            self.time_manager.update(dt)
            
            self._handle_events()
            self._update(dt)
            self._render()
            
            pygame.display.flip()
    
    def _handle_events(self):
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            else:
                self.input_manager.handle_event(event)
    
    def _update(self, dt: float):
        self.input_manager.update()
        current_scene = self.scene_manager.get_current_scene()
        if current_scene:
            current_scene.update(dt)
    
    def _render(self):
        self.ctx.clear(0.1, 0.1, 0.1, 1.0)
        
        current_scene = self.scene_manager.get_current_scene()
        if current_scene:
            self.renderer.render_scene(current_scene)
    
    def shutdown(self):
        self.running = False
        pygame.quit()
        sys.exit()
    
    def get_context(self) -> moderngl.Context:
        return self.ctx
    
    def get_renderer(self) -> Renderer:
        return self.renderer
    
    def get_scene_manager(self) -> SceneManager:
        return self.scene_manager
    
    def get_input_manager(self) -> InputManager:
        return self.input_manager
    
    def get_time_manager(self) -> TimeManager:
        return self.time_manager
    
    def get_asset_manager(self) -> AssetManager:
        return self.asset_manager
