import time

class TimeManager:
    def __init__(self):
        self.delta_time = 0.0
        self.total_time = 0.0
        self.time_scale = 1.0
        self.frame_count = 0
        self.fps = 0.0
        self._last_time = time.time()
        self._fps_timer = 0.0
        self._fps_frame_count = 0
    
    def update(self, dt: float):
        self.delta_time = dt * self.time_scale
        self.total_time += self.delta_time
        self.frame_count += 1
        
        self._fps_timer += dt
        self._fps_frame_count += 1
        
        if self._fps_timer >= 1.0:
            self.fps = self._fps_frame_count / self._fps_timer
            self._fps_timer = 0.0
            self._fps_frame_count = 0
    
    def get_delta_time(self) -> float:
        return self.delta_time
    
    def get_total_time(self) -> float:
        return self.total_time
    
    def get_fps(self) -> float:
        return self.fps
    
    def set_time_scale(self, scale: float):
        self.time_scale = max(0.0, scale)
