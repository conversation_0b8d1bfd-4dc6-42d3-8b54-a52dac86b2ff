from typing import Dict, List, Optional, Any, Type, TypeVar
import uuid

T = TypeVar('T', bound='Component')

class Component:
    def __init__(self):
        self.entity: Optional['Entity'] = None
        self.enabled = True
    
    def start(self):
        pass
    
    def update(self, dt: float):
        pass
    
    def destroy(self):
        pass
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'type': self.__class__.__name__,
            'enabled': self.enabled
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Component':
        component = cls()
        component.enabled = data.get('enabled', True)
        return component

class Transform(Component):
    def __init__(self, position: List[float] = None, rotation: List[float] = None, scale: List[float] = None):
        super().__init__()
        self.position = position or [0.0, 0.0, 0.0]
        self.rotation = rotation or [0.0, 0.0, 0.0]
        self.scale = scale or [1.0, 1.0, 1.0]
        self.parent: Optional['Transform'] = None
        self.children: List['Transform'] = []
    
    def set_parent(self, parent: Optional['Transform']):
        if self.parent:
            self.parent.children.remove(self)
        
        self.parent = parent
        if parent:
            parent.children.append(self)
    
    def get_world_position(self) -> List[float]:
        if self.parent:
            parent_pos = self.parent.get_world_position()
            return [
                self.position[0] + parent_pos[0],
                self.position[1] + parent_pos[1],
                self.position[2] + parent_pos[2]
            ]
        return self.position.copy()
    
    def to_dict(self) -> Dict[str, Any]:
        data = super().to_dict()
        data.update({
            'position': self.position,
            'rotation': self.rotation,
            'scale': self.scale
        })
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Transform':
        transform = cls(
            data.get('position', [0.0, 0.0, 0.0]),
            data.get('rotation', [0.0, 0.0, 0.0]),
            data.get('scale', [1.0, 1.0, 1.0])
        )
        transform.enabled = data.get('enabled', True)
        return transform

class Entity:
    def __init__(self, name: str = "Entity"):
        self.id = str(uuid.uuid4())
        self.name = name
        self.active = True
        self.tags: List[str] = []
        self.components: Dict[Type[Component], Component] = {}
        self.scene: Optional['Scene'] = None
        
        self.add_component(Transform())
    
    def add_component(self, component: Component) -> Component:
        component_type = type(component)
        if component_type in self.components:
            self.components[component_type].destroy()
        
        self.components[component_type] = component
        component.entity = self
        component.start()
        return component
    
    def get_component(self, component_type: Type[T]) -> Optional[T]:
        return self.components.get(component_type)
    
    def has_component(self, component_type: Type[Component]) -> bool:
        return component_type in self.components
    
    def remove_component(self, component_type: Type[Component]) -> bool:
        if component_type in self.components:
            component = self.components[component_type]
            component.destroy()
            component.entity = None
            del self.components[component_type]
            return True
        return False
    
    def add_tag(self, tag: str):
        if tag not in self.tags:
            self.tags.append(tag)
    
    def remove_tag(self, tag: str):
        if tag in self.tags:
            self.tags.remove(tag)
    
    def has_tag(self, tag: str) -> bool:
        return tag in self.tags
    
    def update(self, dt: float):
        for component in self.components.values():
            if component.enabled:
                component.update(dt)
    
    def destroy(self):
        for component in self.components.values():
            component.destroy()
        self.components.clear()
        
        if self.scene:
            self.scene.remove_entity(self.id)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'name': self.name,
            'active': self.active,
            'tags': self.tags,
            'components': {comp_type.__name__: comp.to_dict() 
                          for comp_type, comp in self.components.items()}
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Entity':
        entity = cls(data['name'])
        entity.id = data['id']
        entity.active = data.get('active', True)
        entity.tags = data.get('tags', [])
        
        entity.components.clear()
        
        for comp_name, comp_data in data.get('components', {}).items():
            if comp_name == 'Transform':
                component = Transform.from_dict(comp_data)
                entity.components[Transform] = component
                component.entity = entity
        
        return entity
