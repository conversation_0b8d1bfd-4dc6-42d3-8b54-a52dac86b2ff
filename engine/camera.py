import numpy as np
import pyrr
from typing import List

class Camera:
    def __init__(self):
        self.position = [0.0, 0.0, 0.0]
        self.rotation = [0.0, 0.0, 0.0]
        
        self.fov = 45.0
        self.near_plane = 0.1
        self.far_plane = 1000.0
        
        self.forward = [0.0, 0.0, -1.0]
        self.up = [0.0, 1.0, 0.0]
        self.right = [1.0, 0.0, 0.0]
        
        self.movement_speed = 5.0
        self.mouse_sensitivity = 0.1
        
        self._update_vectors()
    
    def _update_vectors(self):
        yaw = np.radians(self.rotation[1])
        pitch = np.radians(self.rotation[0])
        
        self.forward = [
            np.cos(yaw) * np.cos(pitch),
            np.sin(pitch),
            np.sin(yaw) * np.cos(pitch)
        ]
        
        self.forward = pyrr.vector.normalise(self.forward)
        
        world_up = [0.0, 1.0, 0.0]
        self.right = pyrr.vector.normalise(np.cross(self.forward, world_up))
        self.up = pyrr.vector.normalise(np.cross(self.right, self.forward))
    
    def get_view_matrix(self) -> np.ndarray:
        target = [
            self.position[0] + self.forward[0],
            self.position[1] + self.forward[1],
            self.position[2] + self.forward[2]
        ]
        
        return pyrr.matrix44.create_look_at(
            self.position, target, self.up
        )
    
    def move_forward(self, distance: float):
        self.position[0] += self.forward[0] * distance
        self.position[1] += self.forward[1] * distance
        self.position[2] += self.forward[2] * distance
    
    def move_backward(self, distance: float):
        self.move_forward(-distance)
    
    def move_right(self, distance: float):
        self.position[0] += self.right[0] * distance
        self.position[1] += self.right[1] * distance
        self.position[2] += self.right[2] * distance
    
    def move_left(self, distance: float):
        self.move_right(-distance)
    
    def move_up(self, distance: float):
        self.position[0] += self.up[0] * distance
        self.position[1] += self.up[1] * distance
        self.position[2] += self.up[2] * distance
    
    def move_down(self, distance: float):
        self.move_up(-distance)
    
    def rotate(self, yaw_delta: float, pitch_delta: float):
        self.rotation[1] += yaw_delta * self.mouse_sensitivity
        self.rotation[0] += pitch_delta * self.mouse_sensitivity
        
        self.rotation[0] = max(-89.0, min(89.0, self.rotation[0]))
        
        self._update_vectors()
    
    def set_position(self, position: List[float]):
        self.position = position.copy()
    
    def set_rotation(self, rotation: List[float]):
        self.rotation = rotation.copy()
        self._update_vectors()
    
    def look_at(self, target: List[float]):
        direction = pyrr.vector.normalise([
            target[0] - self.position[0],
            target[1] - self.position[1],
            target[2] - self.position[2]
        ])
        
        self.rotation[0] = np.degrees(np.arcsin(direction[1]))
        self.rotation[1] = np.degrees(np.arctan2(direction[2], direction[0]))
        
        self._update_vectors()

class FPSCamera(Camera):
    def __init__(self):
        super().__init__()
        self.locked_to_ground = True
    
    def move_forward(self, distance: float):
        if self.locked_to_ground:
            forward_ground = pyrr.vector.normalise([self.forward[0], 0.0, self.forward[2]])
            self.position[0] += forward_ground[0] * distance
            self.position[2] += forward_ground[2] * distance
        else:
            super().move_forward(distance)
    
    def move_backward(self, distance: float):
        self.move_forward(-distance)

class OrbitCamera(Camera):
    def __init__(self, target: List[float] = None, distance: float = 5.0):
        super().__init__()
        self.target = target or [0.0, 0.0, 0.0]
        self.distance = distance
        self.min_distance = 1.0
        self.max_distance = 100.0
        
        self._update_position()
    
    def _update_position(self):
        yaw = np.radians(self.rotation[1])
        pitch = np.radians(self.rotation[0])
        
        self.position[0] = self.target[0] + self.distance * np.cos(pitch) * np.cos(yaw)
        self.position[1] = self.target[1] + self.distance * np.sin(pitch)
        self.position[2] = self.target[2] + self.distance * np.cos(pitch) * np.sin(yaw)
        
        self._update_vectors()
    
    def rotate(self, yaw_delta: float, pitch_delta: float):
        super().rotate(yaw_delta, pitch_delta)
        self._update_position()
    
    def zoom(self, delta: float):
        self.distance = max(self.min_distance, min(self.max_distance, self.distance + delta))
        self._update_position()
    
    def set_target(self, target: List[float]):
        self.target = target.copy()
        self._update_position()
