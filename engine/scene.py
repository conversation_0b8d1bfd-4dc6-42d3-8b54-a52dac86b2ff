from typing import Dict, List, Optional, Any
import uuid
from .entity import Entity

class Scene:
    def __init__(self, name: str = "Untitled Scene"):
        self.name = name
        self.id = str(uuid.uuid4())
        self.entities: Dict[str, Entity] = {}
        self.active = True
        
        self.ambient_light = [0.2, 0.2, 0.2]
        self.directional_light = {
            'direction': [0.0, -1.0, -0.5],
            'color': [1.0, 1.0, 1.0],
            'intensity': 1.0
        }
        
        self.gravity = [0.0, -9.81, 0.0]
        self.physics_enabled = True
    
    def add_entity(self, entity: Entity) -> str:
        entity_id = entity.id
        self.entities[entity_id] = entity
        entity.scene = self
        return entity_id
    
    def remove_entity(self, entity_id: str) -> bool:
        if entity_id in self.entities:
            entity = self.entities[entity_id]
            entity.scene = None
            del self.entities[entity_id]
            return True
        return False
    
    def get_entity(self, entity_id: str) -> Optional[Entity]:
        return self.entities.get(entity_id)
    
    def get_entities_by_tag(self, tag: str) -> List[Entity]:
        return [entity for entity in self.entities.values() if tag in entity.tags]
    
    def get_entities_with_component(self, component_type: type) -> List[Entity]:
        return [entity for entity in self.entities.values() 
                if entity.has_component(component_type)]
    
    def update(self, dt: float):
        if not self.active:
            return
            
        for entity in self.entities.values():
            if entity.active:
                entity.update(dt)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'name': self.name,
            'id': self.id,
            'ambient_light': self.ambient_light,
            'directional_light': self.directional_light,
            'gravity': self.gravity,
            'physics_enabled': self.physics_enabled,
            'entities': {eid: entity.to_dict() for eid, entity in self.entities.items()}
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Scene':
        scene = cls(data['name'])
        scene.id = data['id']
        scene.ambient_light = data.get('ambient_light', [0.2, 0.2, 0.2])
        scene.directional_light = data.get('directional_light', {
            'direction': [0.0, -1.0, -0.5],
            'color': [1.0, 1.0, 1.0],
            'intensity': 1.0
        })
        scene.gravity = data.get('gravity', [0.0, -9.81, 0.0])
        scene.physics_enabled = data.get('physics_enabled', True)
        
        for entity_data in data.get('entities', {}).values():
            entity = Entity.from_dict(entity_data)
            scene.add_entity(entity)
        
        return scene

class SceneManager:
    def __init__(self):
        self.scenes: Dict[str, Scene] = {}
        self.current_scene_id: Optional[str] = None
        self.default_scene = Scene("Default Scene")
        self.add_scene(self.default_scene)
        self.set_current_scene(self.default_scene.id)
    
    def add_scene(self, scene: Scene) -> str:
        self.scenes[scene.id] = scene
        return scene.id
    
    def remove_scene(self, scene_id: str) -> bool:
        if scene_id in self.scenes and scene_id != self.current_scene_id:
            del self.scenes[scene_id]
            return True
        return False
    
    def get_scene(self, scene_id: str) -> Optional[Scene]:
        return self.scenes.get(scene_id)
    
    def get_current_scene(self) -> Optional[Scene]:
        if self.current_scene_id:
            return self.scenes.get(self.current_scene_id)
        return None
    
    def set_current_scene(self, scene_id: str) -> bool:
        if scene_id in self.scenes:
            if self.current_scene_id:
                current = self.scenes[self.current_scene_id]
                current.active = False
            
            self.current_scene_id = scene_id
            self.scenes[scene_id].active = True
            return True
        return False
    
    def create_scene(self, name: str = "New Scene") -> Scene:
        scene = Scene(name)
        self.add_scene(scene)
        return scene
