import os
import json
import shutil
import zipfile
from typing import Dict, Any, Optional

class GameBuilder:
    def __init__(self):
        self.build_config: Dict[str, Any] = {}
    
    def build_game(self, project_path: str, output_path: str = None) -> bool:
        try:
            if not output_path:
                output_path = os.path.join(project_path, "Build")
            
            os.makedirs(output_path, exist_ok=True)
            
            project_file = os.path.join(project_path, "project.json")
            if not os.path.exists(project_file):
                print("Project file not found")
                return False
            
            with open(project_file, 'r') as f:
                project_data = json.load(f)
            
            game_name = project_data.get("name", "Game")
            build_folder = os.path.join(output_path, f"{game_name}_Build")
            
            if os.path.exists(build_folder):
                shutil.rmtree(build_folder)
            os.makedirs(build_folder)
            
            self._copy_game_runtime(build_folder)
            self._copy_project_assets(project_path, build_folder)
            self._copy_project_scenes(project_path, build_folder)
            self._create_game_config(project_data, build_folder)
            
            zip_path = os.path.join(output_path, f"{game_name}.zip")
            self._create_zip_package(build_folder, zip_path)
            
            print(f"Game built successfully: {zip_path}")
            return True
            
        except Exception as e:
            print(f"Failed to build game: {e}")
            return False
    
    def build_server(self, project_path: str, output_path: str = None) -> bool:
        try:
            if not output_path:
                output_path = os.path.join(project_path, "Build")
            
            os.makedirs(output_path, exist_ok=True)
            
            project_file = os.path.join(project_path, "project.json")
            if not os.path.exists(project_file):
                print("Project file not found")
                return False
            
            with open(project_file, 'r') as f:
                project_data = json.load(f)
            
            game_name = project_data.get("name", "Game")
            server_folder = os.path.join(output_path, f"{game_name}_Server")
            
            if os.path.exists(server_folder):
                shutil.rmtree(server_folder)
            os.makedirs(server_folder)
            
            self._copy_server_runtime(server_folder)
            self._copy_server_assets(project_path, server_folder)
            self._copy_project_scenes(project_path, server_folder)
            self._create_server_config(project_data, server_folder)
            
            zip_path = os.path.join(output_path, f"{game_name}_Server.zip")
            self._create_zip_package(server_folder, zip_path)
            
            print(f"Server built successfully: {zip_path}")
            return True
            
        except Exception as e:
            print(f"Failed to build server: {e}")
            return False
    
    def _copy_game_runtime(self, build_folder: str):
        runtime_files = [
            "player/game_player.py",
            "engine/__init__.py",
            "engine/core.py",
            "engine/scene.py",
            "engine/entity.py",
            "engine/renderer.py",
            "engine/camera.py",
            "engine/input_manager.py",
            "engine/time_manager.py",
            "engine/asset_manager.py",
            "engine/components.py"
        ]
        
        engine_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        for file_path in runtime_files:
            src_path = os.path.join(engine_root, file_path)
            if os.path.exists(src_path):
                dst_path = os.path.join(build_folder, file_path)
                os.makedirs(os.path.dirname(dst_path), exist_ok=True)
                shutil.copy2(src_path, dst_path)
        
        requirements_src = os.path.join(engine_root, "requirements.txt")
        if os.path.exists(requirements_src):
            shutil.copy2(requirements_src, os.path.join(build_folder, "requirements.txt"))
    
    def _copy_server_runtime(self, build_folder: str):
        runtime_files = [
            "server/game_server.py",
            "engine/__init__.py",
            "engine/core.py",
            "engine/scene.py",
            "engine/entity.py",
            "engine/time_manager.py",
            "engine/asset_manager.py",
            "engine/components.py"
        ]
        
        engine_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        for file_path in runtime_files:
            src_path = os.path.join(engine_root, file_path)
            if os.path.exists(src_path):
                dst_path = os.path.join(build_folder, file_path)
                os.makedirs(os.path.dirname(dst_path), exist_ok=True)
                shutil.copy2(src_path, dst_path)
    
    def _copy_project_assets(self, project_path: str, build_folder: str):
        assets_src = os.path.join(project_path, "Assets")
        assets_dst = os.path.join(build_folder, "Assets")
        
        if os.path.exists(assets_src):
            shutil.copytree(assets_src, assets_dst, dirs_exist_ok=True)
    
    def _copy_server_assets(self, project_path: str, build_folder: str):
        assets_src = os.path.join(project_path, "Assets")
        assets_dst = os.path.join(build_folder, "Assets")
        
        if os.path.exists(assets_src):
            os.makedirs(assets_dst, exist_ok=True)
            
            for item in os.listdir(assets_src):
                item_path = os.path.join(assets_src, item)
                if os.path.isdir(item_path):
                    if item in ["Models", "Sounds", "Materials"]:
                        shutil.copytree(item_path, os.path.join(assets_dst, item), dirs_exist_ok=True)
                else:
                    shutil.copy2(item_path, assets_dst)
    
    def _copy_project_scenes(self, project_path: str, build_folder: str):
        scenes_src = os.path.join(project_path, "Scenes")
        scenes_dst = os.path.join(build_folder, "Scenes")
        
        if os.path.exists(scenes_src):
            shutil.copytree(scenes_src, scenes_dst, dirs_exist_ok=True)
    
    def _create_game_config(self, project_data: Dict[str, Any], build_folder: str):
        game_config = {
            "name": project_data.get("name", "Game"),
            "version": project_data.get("version", "1.0.0"),
            "default_scene": project_data.get("settings", {}).get("default_scene", "main_scene"),
            "player_settings": project_data.get("settings", {}).get("player_settings", {}),
            "multiplayer_settings": project_data.get("settings", {}).get("multiplayer_settings", {})
        }
        
        config_path = os.path.join(build_folder, "game_config.json")
        with open(config_path, 'w') as f:
            json.dump(game_config, f, indent=2)
    
    def _create_server_config(self, project_data: Dict[str, Any], build_folder: str):
        server_config = {
            "name": project_data.get("name", "Game") + " Server",
            "version": project_data.get("version", "1.0.0"),
            "default_scene": project_data.get("settings", {}).get("default_scene", "main_scene"),
            "multiplayer_settings": project_data.get("settings", {}).get("multiplayer_settings", {}),
            "server_only": True
        }
        
        config_path = os.path.join(build_folder, "server_config.json")
        with open(config_path, 'w') as f:
            json.dump(server_config, f, indent=2)
    
    def _create_zip_package(self, source_folder: str, zip_path: str):
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(source_folder):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, source_folder)
                    zipf.write(file_path, arc_name)
        
        shutil.rmtree(source_folder)
