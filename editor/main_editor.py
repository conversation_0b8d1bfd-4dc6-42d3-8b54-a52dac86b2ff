import sys
import os
import pygame
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                            QWidget, QSplitter, QMenuBar, QMenu, QFileDialog, 
                            QMessageBox, QStatusBar, QToolBar, QDockWidget)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QAction, QIcon

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from engine import GameEngine
from .scene_editor import SceneEditor
from .asset_browser import AssetBrowser
from .inspector import Inspector
from .hierarchy import Hierarchy
from .project_manager import ProjectManager

class GameEditor(QMainWindow):
    def __init__(self):
        super().__init__()
        
        self.engine = None
        self.project_manager = ProjectManager()
        self.current_project_path = None
        
        self.scene_editor = None
        self.asset_browser = None
        self.inspector = None
        self.hierarchy = None
        
        self.init_ui()
        self.setup_engine()
        
        # self.update_timer = QTimer()
        # self.update_timer.timeout.connect(self.update_engine)
        # self.update_timer.start(16)
    
    def init_ui(self):
        self.setWindowTitle("3D Game Engine Editor")
        self.setGeometry(100, 100, 1600, 900)
        
        self.create_menu_bar()
        self.create_toolbar()
        self.create_dock_widgets()
        self.create_central_widget()
        self.create_status_bar()
    
    def create_menu_bar(self):
        menubar = self.menuBar()
        
        file_menu = menubar.addMenu('File')
        
        new_project_action = QAction('New Project', self)
        new_project_action.triggered.connect(self.new_project)
        file_menu.addAction(new_project_action)
        
        open_project_action = QAction('Open Project', self)
        open_project_action.triggered.connect(self.open_project)
        file_menu.addAction(open_project_action)
        
        save_project_action = QAction('Save Project', self)
        save_project_action.triggered.connect(self.save_project)
        file_menu.addAction(save_project_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('Exit', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        edit_menu = menubar.addMenu('Edit')
        
        undo_action = QAction('Undo', self)
        edit_menu.addAction(undo_action)
        
        redo_action = QAction('Redo', self)
        edit_menu.addAction(redo_action)
        
        view_menu = menubar.addMenu('View')
        
        reset_layout_action = QAction('Reset Layout', self)
        reset_layout_action.triggered.connect(self.reset_layout)
        view_menu.addAction(reset_layout_action)
        
        gameobject_menu = menubar.addMenu('GameObject')
        
        create_empty_action = QAction('Create Empty', self)
        create_empty_action.triggered.connect(self.create_empty_gameobject)
        gameobject_menu.addAction(create_empty_action)
        
        create_cube_action = QAction('Create Cube', self)
        create_cube_action.triggered.connect(self.create_cube)
        gameobject_menu.addAction(create_cube_action)
        
        create_sphere_action = QAction('Create Sphere', self)
        create_sphere_action.triggered.connect(self.create_sphere)
        gameobject_menu.addAction(create_sphere_action)
        
        create_plane_action = QAction('Create Plane', self)
        create_plane_action.triggered.connect(self.create_plane)
        gameobject_menu.addAction(create_plane_action)
        
        build_menu = menubar.addMenu('Build')
        
        build_game_action = QAction('Build Game', self)
        build_game_action.triggered.connect(self.build_game)
        build_menu.addAction(build_game_action)
        
        build_server_action = QAction('Build Server', self)
        build_server_action.triggered.connect(self.build_server)
        build_menu.addAction(build_server_action)
    
    def create_toolbar(self):
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        play_action = QAction('Play', self)
        play_action.triggered.connect(self.play_game)
        toolbar.addAction(play_action)
        
        pause_action = QAction('Pause', self)
        pause_action.triggered.connect(self.pause_game)
        toolbar.addAction(pause_action)
        
        stop_action = QAction('Stop', self)
        stop_action.triggered.connect(self.stop_game)
        toolbar.addAction(stop_action)
    
    def create_dock_widgets(self):
        self.hierarchy = Hierarchy()
        hierarchy_dock = QDockWidget("Hierarchy", self)
        hierarchy_dock.setWidget(self.hierarchy)
        self.addDockWidget(Qt.DockWidgetArea.LeftDockWidgetArea, hierarchy_dock)
        
        self.inspector = Inspector()
        inspector_dock = QDockWidget("Inspector", self)
        inspector_dock.setWidget(self.inspector)
        self.addDockWidget(Qt.DockWidgetArea.RightDockWidgetArea, inspector_dock)
        
        self.asset_browser = AssetBrowser()
        asset_dock = QDockWidget("Assets", self)
        asset_dock.setWidget(self.asset_browser)
        self.addDockWidget(Qt.DockWidgetArea.BottomDockWidgetArea, asset_dock)
    
    def create_central_widget(self):
        self.scene_editor = SceneEditor()
        self.setCentralWidget(self.scene_editor)
    
    def create_status_bar(self):
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")
    
    def setup_engine(self):
        try:
            self.engine = GameEngine(1280, 720, "Game Engine - Scene View")
            
            if self.scene_editor:
                self.scene_editor.set_engine(self.engine)
            if self.hierarchy:
                self.hierarchy.set_engine(self.engine)
                self.hierarchy.entity_selected.connect(self.on_entity_selected)
            if self.inspector:
                self.inspector.set_engine(self.engine)
            if self.asset_browser:
                self.asset_browser.set_engine(self.engine)
                
        except Exception as e:
            QMessageBox.critical(self, "Engine Error", f"Failed to initialize game engine: {e}")
    
    def update_engine(self):
        if self.engine and self.scene_editor:
            self.scene_editor.update()
    
    def new_project(self):
        folder = QFileDialog.getExistingDirectory(self, "Select Project Folder")
        if folder:
            self.project_manager.create_project(folder)
            self.current_project_path = folder
            self.status_bar.showMessage(f"Created new project: {folder}")
    
    def open_project(self):
        folder = QFileDialog.getExistingDirectory(self, "Select Project Folder")
        if folder:
            if self.project_manager.load_project(folder):
                self.current_project_path = folder
                self.status_bar.showMessage(f"Opened project: {folder}")
            else:
                QMessageBox.warning(self, "Project Error", "Failed to open project")
    
    def save_project(self):
        if self.current_project_path:
            self.project_manager.save_project(self.current_project_path)
            self.status_bar.showMessage("Project saved")
        else:
            QMessageBox.warning(self, "Save Error", "No project to save")
    
    def reset_layout(self):
        pass
    
    def create_empty_gameobject(self):
        if self.engine:
            from engine.entity import Entity
            entity = Entity("Empty GameObject")
            scene = self.engine.get_scene_manager().get_current_scene()
            if scene:
                scene.add_entity(entity)
                if self.hierarchy:
                    self.hierarchy.refresh()
    
    def create_cube(self):
        pass
    
    def create_sphere(self):
        pass
    
    def create_plane(self):
        pass
    
    def build_game(self):
        if self.current_project_path:
            from .game_builder import GameBuilder
            builder = GameBuilder()
            builder.build_game(self.current_project_path)
            self.status_bar.showMessage("Game built successfully")
        else:
            QMessageBox.warning(self, "Build Error", "No project to build")
    
    def build_server(self):
        if self.current_project_path:
            from .game_builder import GameBuilder
            builder = GameBuilder()
            builder.build_server(self.current_project_path)
            self.status_bar.showMessage("Server built successfully")
        else:
            QMessageBox.warning(self, "Build Error", "No project to build")
    
    def play_game(self):
        self.status_bar.showMessage("Playing...")
    
    def pause_game(self):
        self.status_bar.showMessage("Paused")
    
    def stop_game(self):
        self.status_bar.showMessage("Stopped")

    def on_entity_selected(self, entity_id: str):
        if self.inspector:
            self.inspector.set_entity(entity_id)

    def run(self):
        app = QApplication(sys.argv)
        self.show()
        sys.exit(app.exec())

    def closeEvent(self, event):
        if self.engine:
            self.engine.shutdown()
        event.accept()
