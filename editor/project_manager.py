import os
import json
import shutil
from typing import Dict, Any, Optional

class ProjectManager:
    def __init__(self):
        self.project_data: Dict[str, Any] = {}
        self.project_path: Optional[str] = None
    
    def create_project(self, project_path: str, project_name: str = None) -> bool:
        try:
            if not project_name:
                project_name = os.path.basename(project_path)
            
            os.makedirs(project_path, exist_ok=True)
            
            self.project_data = {
                "name": project_name,
                "version": "1.0.0",
                "engine_version": "1.0.0",
                "created_date": "",
                "last_modified": "",
                "settings": {
                    "default_scene": "main_scene",
                    "build_settings": {
                        "target_platform": "windows",
                        "build_type": "development",
                        "compression": True
                    },
                    "player_settings": {
                        "company_name": "",
                        "product_name": project_name,
                        "version": "1.0.0",
                        "default_resolution": [1280, 720],
                        "fullscreen": False
                    },
                    "multiplayer_settings": {
                        "enabled": False,
                        "max_players": 4,
                        "server_port": 7777,
                        "use_dedicated_server": False
                    }
                },
                "scenes": ["main_scene"],
                "assets": []
            }
            
            self.project_path = project_path
            
            self._create_project_structure()
            self._create_default_scene()
            self.save_project(project_path)
            
            return True
        except Exception as e:
            print(f"Failed to create project: {e}")
            return False
    
    def _create_project_structure(self):
        if not self.project_path:
            return
        
        folders = [
            "Assets",
            "Assets/Textures",
            "Assets/Models", 
            "Assets/Sounds",
            "Assets/Materials",
            "Assets/Shaders",
            "Assets/Scripts",
            "Scenes",
            "Build",
            "Temp"
        ]
        
        for folder in folders:
            folder_path = os.path.join(self.project_path, folder)
            os.makedirs(folder_path, exist_ok=True)
    
    def _create_default_scene(self):
        if not self.project_path:
            return
        
        scene_data = {
            "name": "Main Scene",
            "id": "main_scene",
            "ambient_light": [0.2, 0.2, 0.2],
            "directional_light": {
                "direction": [0.0, -1.0, -0.5],
                "color": [1.0, 1.0, 1.0],
                "intensity": 1.0
            },
            "gravity": [0.0, -9.81, 0.0],
            "physics_enabled": True,
            "entities": {}
        }
        
        scene_path = os.path.join(self.project_path, "Scenes", "main_scene.json")
        with open(scene_path, 'w') as f:
            json.dump(scene_data, f, indent=2)
    
    def load_project(self, project_path: str) -> bool:
        try:
            project_file = os.path.join(project_path, "project.json")
            if not os.path.exists(project_file):
                return False
            
            with open(project_file, 'r') as f:
                self.project_data = json.load(f)
            
            self.project_path = project_path
            return True
        except Exception as e:
            print(f"Failed to load project: {e}")
            return False
    
    def save_project(self, project_path: str = None) -> bool:
        try:
            if project_path:
                self.project_path = project_path
            
            if not self.project_path:
                return False
            
            project_file = os.path.join(self.project_path, "project.json")
            with open(project_file, 'w') as f:
                json.dump(self.project_data, f, indent=2)
            
            return True
        except Exception as e:
            print(f"Failed to save project: {e}")
            return False
    
    def get_project_data(self) -> Dict[str, Any]:
        return self.project_data
    
    def update_project_setting(self, key_path: str, value: Any):
        keys = key_path.split('.')
        current = self.project_data
        
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        current[keys[-1]] = value
    
    def get_project_setting(self, key_path: str, default: Any = None) -> Any:
        keys = key_path.split('.')
        current = self.project_data
        
        try:
            for key in keys:
                current = current[key]
            return current
        except (KeyError, TypeError):
            return default
    
    def add_scene(self, scene_name: str) -> bool:
        if "scenes" not in self.project_data:
            self.project_data["scenes"] = []
        
        if scene_name not in self.project_data["scenes"]:
            self.project_data["scenes"].append(scene_name)
            return True
        return False
    
    def remove_scene(self, scene_name: str) -> bool:
        if "scenes" in self.project_data and scene_name in self.project_data["scenes"]:
            self.project_data["scenes"].remove(scene_name)
            
            scene_file = os.path.join(self.project_path, "Scenes", f"{scene_name}.json")
            if os.path.exists(scene_file):
                os.remove(scene_file)
            
            return True
        return False
    
    def get_scenes(self) -> list:
        return self.project_data.get("scenes", [])
    
    def add_asset(self, asset_name: str, asset_type: str, asset_path: str) -> bool:
        if "assets" not in self.project_data:
            self.project_data["assets"] = []
        
        asset_info = {
            "name": asset_name,
            "type": asset_type,
            "path": asset_path
        }
        
        for existing_asset in self.project_data["assets"]:
            if existing_asset["name"] == asset_name:
                existing_asset.update(asset_info)
                return True
        
        self.project_data["assets"].append(asset_info)
        return True
    
    def remove_asset(self, asset_name: str) -> bool:
        if "assets" in self.project_data:
            for i, asset in enumerate(self.project_data["assets"]):
                if asset["name"] == asset_name:
                    del self.project_data["assets"][i]
                    return True
        return False
    
    def get_assets(self) -> list:
        return self.project_data.get("assets", [])
    
    def get_build_path(self) -> str:
        if self.project_path:
            return os.path.join(self.project_path, "Build")
        return ""
    
    def get_assets_path(self) -> str:
        if self.project_path:
            return os.path.join(self.project_path, "Assets")
        return ""
    
    def get_scenes_path(self) -> str:
        if self.project_path:
            return os.path.join(self.project_path, "Scenes")
        return ""
