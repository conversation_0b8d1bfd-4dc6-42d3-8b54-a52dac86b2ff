from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel
from PyQt6.QtCore import Qt
from PyQt6.QtOpenGLWidgets import QOpenGLWidget
from PyQt6.QtOpenGL import QOpenGLVersionProfile
import pygame
import sys

class SceneEditor(QWidget):
    def __init__(self):
        super().__init__()
        self.engine = None
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        self.scene_view = QLabel("Scene View - Engine will be embedded here")
        self.scene_view.setMinimumSize(800, 600)
        self.scene_view.setStyleSheet("background-color: #2b2b2b; color: white; border: 1px solid #555;")
        self.scene_view.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        layout.addWidget(self.scene_view)
        self.setLayout(layout)
    
    def set_engine(self, engine):
        self.engine = engine
        if engine:
            self.scene_view.setText("3D Scene View Active")
    
    def update(self):
        if self.engine:
            pass
