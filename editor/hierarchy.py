from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QTreeWidget, QTreeWidgetItem, 
                            QHBoxLayout, QPushButton, QMenu, QMessageBox)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QAction

class Hierarchy(QWidget):
    entity_selected = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.engine = None
        self.selected_entity_id = None
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        button_layout = QHBoxLayout()
        
        self.create_button = QPushButton("Create")
        self.create_button.clicked.connect(self.show_create_menu)
        button_layout.addWidget(self.create_button)
        
        self.delete_button = QPushButton("Delete")
        self.delete_button.clicked.connect(self.delete_selected)
        self.delete_button.setEnabled(False)
        button_layout.addWidget(self.delete_button)
        
        layout.addLayout(button_layout)
        
        self.tree = QTreeWidget()
        self.tree.setHeaderLabel("Scene Objects")
        self.tree.itemSelectionChanged.connect(self.on_selection_changed)
        self.tree.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.tree.customContextMenuRequested.connect(self.show_context_menu)
        
        layout.addWidget(self.tree)
        self.setLayout(layout)
    
    def set_engine(self, engine):
        self.engine = engine
        self.refresh()
    
    def refresh(self):
        self.tree.clear()
        
        if not self.engine:
            return
        
        scene = self.engine.get_scene_manager().get_current_scene()
        if not scene:
            return
        
        for entity_id, entity in scene.entities.items():
            item = QTreeWidgetItem([entity.name])
            item.setData(0, Qt.ItemDataRole.UserRole, entity_id)
            self.tree.addTopLevelItem(item)
    
    def on_selection_changed(self):
        selected_items = self.tree.selectedItems()
        if selected_items:
            item = selected_items[0]
            entity_id = item.data(0, Qt.ItemDataRole.UserRole)
            self.selected_entity_id = entity_id
            self.delete_button.setEnabled(True)
            self.entity_selected.emit(entity_id)
        else:
            self.selected_entity_id = None
            self.delete_button.setEnabled(False)
    
    def show_create_menu(self):
        menu = QMenu(self)
        
        empty_action = QAction("Empty GameObject", self)
        empty_action.triggered.connect(self.create_empty)
        menu.addAction(empty_action)
        
        menu.addSeparator()
        
        cube_action = QAction("Cube", self)
        cube_action.triggered.connect(self.create_cube)
        menu.addAction(cube_action)
        
        sphere_action = QAction("Sphere", self)
        sphere_action.triggered.connect(self.create_sphere)
        menu.addAction(sphere_action)
        
        plane_action = QAction("Plane", self)
        plane_action.triggered.connect(self.create_plane)
        menu.addAction(plane_action)
        
        menu.addSeparator()
        
        light_action = QAction("Light", self)
        light_action.triggered.connect(self.create_light)
        menu.addAction(light_action)
        
        camera_action = QAction("Camera", self)
        camera_action.triggered.connect(self.create_camera)
        menu.addAction(camera_action)
        
        menu.exec(self.create_button.mapToGlobal(self.create_button.rect().bottomLeft()))
    
    def show_context_menu(self, position):
        item = self.tree.itemAt(position)
        if item:
            menu = QMenu(self)
            
            rename_action = QAction("Rename", self)
            menu.addAction(rename_action)
            
            duplicate_action = QAction("Duplicate", self)
            menu.addAction(duplicate_action)
            
            delete_action = QAction("Delete", self)
            delete_action.triggered.connect(self.delete_selected)
            menu.addAction(delete_action)
            
            menu.exec(self.tree.mapToGlobal(position))
    
    def create_empty(self):
        if self.engine:
            from engine.entity import Entity
            entity = Entity("Empty GameObject")
            scene = self.engine.get_scene_manager().get_current_scene()
            if scene:
                scene.add_entity(entity)
                self.refresh()
    
    def create_cube(self):
        if self.engine:
            from engine.entity import Entity
            from engine.components import MeshRenderer
            entity = Entity("Cube")
            entity.add_component(MeshRenderer())
            scene = self.engine.get_scene_manager().get_current_scene()
            if scene:
                scene.add_entity(entity)
                self.refresh()
    
    def create_sphere(self):
        if self.engine:
            from engine.entity import Entity
            from engine.components import MeshRenderer
            entity = Entity("Sphere")
            entity.add_component(MeshRenderer())
            scene = self.engine.get_scene_manager().get_current_scene()
            if scene:
                scene.add_entity(entity)
                self.refresh()
    
    def create_plane(self):
        if self.engine:
            from engine.entity import Entity
            from engine.components import MeshRenderer
            entity = Entity("Plane")
            entity.add_component(MeshRenderer())
            scene = self.engine.get_scene_manager().get_current_scene()
            if scene:
                scene.add_entity(entity)
                self.refresh()
    
    def create_light(self):
        if self.engine:
            from engine.entity import Entity
            from engine.components import Light
            entity = Entity("Light")
            entity.add_component(Light())
            scene = self.engine.get_scene_manager().get_current_scene()
            if scene:
                scene.add_entity(entity)
                self.refresh()
    
    def create_camera(self):
        if self.engine:
            from engine.entity import Entity
            entity = Entity("Camera")
            scene = self.engine.get_scene_manager().get_current_scene()
            if scene:
                scene.add_entity(entity)
                self.refresh()
    
    def delete_selected(self):
        if self.selected_entity_id and self.engine:
            scene = self.engine.get_scene_manager().get_current_scene()
            if scene:
                scene.remove_entity(self.selected_entity_id)
                self.refresh()
                self.selected_entity_id = None
                self.delete_button.setEnabled(False)
