from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QLabel, QLineEdit, QSpinBox, 
                            QDoubleSpinBox, QCheckBox, QComboBox, QPushButton,
                            QGroupBox, QFormLayout, QScrollArea)
from PyQt6.QtCore import Qt

class Inspector(QWidget):
    def __init__(self):
        super().__init__()
        self.engine = None
        self.current_entity = None
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        self.scroll_area = QScrollArea()
        self.scroll_widget = QWidget()
        self.scroll_layout = QVBoxLayout(self.scroll_widget)
        
        self.entity_name_label = QLabel("No entity selected")
        self.entity_name_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        self.scroll_layout.addWidget(self.entity_name_label)
        
        self.transform_group = self.create_transform_group()
        self.scroll_layout.addWidget(self.transform_group)
        
        self.components_layout = QVBoxLayout()
        self.scroll_layout.addLayout(self.components_layout)
        
        self.add_component_button = QPushButton("Add Component")
        self.add_component_button.clicked.connect(self.show_add_component_menu)
        self.scroll_layout.addWidget(self.add_component_button)
        
        self.scroll_layout.addStretch()
        
        self.scroll_area.setWidget(self.scroll_widget)
        self.scroll_area.setWidgetResizable(True)
        
        layout.addWidget(self.scroll_area)
        self.setLayout(layout)
        
        self.set_enabled(False)
    
    def create_transform_group(self):
        group = QGroupBox("Transform")
        layout = QFormLayout()
        
        self.position_x = QDoubleSpinBox()
        self.position_x.setRange(-1000, 1000)
        self.position_x.setDecimals(3)
        self.position_x.valueChanged.connect(self.update_transform)
        
        self.position_y = QDoubleSpinBox()
        self.position_y.setRange(-1000, 1000)
        self.position_y.setDecimals(3)
        self.position_y.valueChanged.connect(self.update_transform)
        
        self.position_z = QDoubleSpinBox()
        self.position_z.setRange(-1000, 1000)
        self.position_z.setDecimals(3)
        self.position_z.valueChanged.connect(self.update_transform)
        
        layout.addRow("Position X:", self.position_x)
        layout.addRow("Position Y:", self.position_y)
        layout.addRow("Position Z:", self.position_z)
        
        self.rotation_x = QDoubleSpinBox()
        self.rotation_x.setRange(-360, 360)
        self.rotation_x.setDecimals(3)
        self.rotation_x.valueChanged.connect(self.update_transform)
        
        self.rotation_y = QDoubleSpinBox()
        self.rotation_y.setRange(-360, 360)
        self.rotation_y.setDecimals(3)
        self.rotation_y.valueChanged.connect(self.update_transform)
        
        self.rotation_z = QDoubleSpinBox()
        self.rotation_z.setRange(-360, 360)
        self.rotation_z.setDecimals(3)
        self.rotation_z.valueChanged.connect(self.update_transform)
        
        layout.addRow("Rotation X:", self.rotation_x)
        layout.addRow("Rotation Y:", self.rotation_y)
        layout.addRow("Rotation Z:", self.rotation_z)
        
        self.scale_x = QDoubleSpinBox()
        self.scale_x.setRange(0.001, 1000)
        self.scale_x.setDecimals(3)
        self.scale_x.setValue(1.0)
        self.scale_x.valueChanged.connect(self.update_transform)
        
        self.scale_y = QDoubleSpinBox()
        self.scale_y.setRange(0.001, 1000)
        self.scale_y.setDecimals(3)
        self.scale_y.setValue(1.0)
        self.scale_y.valueChanged.connect(self.update_transform)
        
        self.scale_z = QDoubleSpinBox()
        self.scale_z.setRange(0.001, 1000)
        self.scale_z.setDecimals(3)
        self.scale_z.setValue(1.0)
        self.scale_z.valueChanged.connect(self.update_transform)
        
        layout.addRow("Scale X:", self.scale_x)
        layout.addRow("Scale Y:", self.scale_y)
        layout.addRow("Scale Z:", self.scale_z)
        
        group.setLayout(layout)
        return group
    
    def set_engine(self, engine):
        self.engine = engine
    
    def set_entity(self, entity_id):
        if not self.engine:
            return
        
        scene = self.engine.get_scene_manager().get_current_scene()
        if not scene:
            return
        
        entity = scene.get_entity(entity_id)
        if entity:
            self.current_entity = entity
            self.entity_name_label.setText(f"Entity: {entity.name}")
            self.load_entity_data()
            self.set_enabled(True)
        else:
            self.current_entity = None
            self.entity_name_label.setText("No entity selected")
            self.set_enabled(False)
    
    def load_entity_data(self):
        if not self.current_entity:
            return
        
        from engine.entity import Transform
        transform = self.current_entity.get_component(Transform)
        if transform:
            self.position_x.setValue(transform.position[0])
            self.position_y.setValue(transform.position[1])
            self.position_z.setValue(transform.position[2])
            
            self.rotation_x.setValue(transform.rotation[0])
            self.rotation_y.setValue(transform.rotation[1])
            self.rotation_z.setValue(transform.rotation[2])
            
            self.scale_x.setValue(transform.scale[0])
            self.scale_y.setValue(transform.scale[1])
            self.scale_z.setValue(transform.scale[2])
        
        self.refresh_components()
    
    def update_transform(self):
        if not self.current_entity:
            return
        
        from engine.entity import Transform
        transform = self.current_entity.get_component(Transform)
        if transform:
            transform.position = [
                self.position_x.value(),
                self.position_y.value(),
                self.position_z.value()
            ]
            transform.rotation = [
                self.rotation_x.value(),
                self.rotation_y.value(),
                self.rotation_z.value()
            ]
            transform.scale = [
                self.scale_x.value(),
                self.scale_y.value(),
                self.scale_z.value()
            ]
    
    def refresh_components(self):
        for i in reversed(range(self.components_layout.count())):
            child = self.components_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
        
        if not self.current_entity:
            return
        
        from engine.entity import Transform
        for component_type, component in self.current_entity.components.items():
            if component_type != Transform:
                component_widget = self.create_component_widget(component)
                self.components_layout.addWidget(component_widget)
    
    def create_component_widget(self, component):
        group = QGroupBox(component.__class__.__name__)
        layout = QFormLayout()
        
        enabled_checkbox = QCheckBox("Enabled")
        enabled_checkbox.setChecked(component.enabled)
        layout.addRow(enabled_checkbox)
        
        group.setLayout(layout)
        return group
    
    def show_add_component_menu(self):
        pass
    
    def set_enabled(self, enabled):
        self.transform_group.setEnabled(enabled)
        self.add_component_button.setEnabled(enabled)
