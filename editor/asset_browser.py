from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QListWidget, 
                            QListWidgetItem, QPushButton, QFileDialog, QLabel,
                            QComboBox, QLineEdit, QMessageBox)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon, QPixmap
import os

class AssetBrowser(QWidget):
    asset_selected = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.engine = None
        self.current_filter = "All"
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        toolbar_layout = QHBoxLayout()
        
        self.import_button = QPushButton("Import")
        self.import_button.clicked.connect(self.import_asset)
        toolbar_layout.addWidget(self.import_button)
        
        self.refresh_button = QPushButton("Refresh")
        self.refresh_button.clicked.connect(self.refresh_assets)
        toolbar_layout.addWidget(self.refresh_button)
        
        toolbar_layout.addStretch()
        
        self.filter_combo = QComboBox()
        self.filter_combo.addItems(["All", "Textures", "Models", "Sounds", "Materials", "Shaders"])
        self.filter_combo.currentTextChanged.connect(self.filter_changed)
        toolbar_layout.addWidget(QLabel("Filter:"))
        toolbar_layout.addWidget(self.filter_combo)
        
        layout.addLayout(toolbar_layout)
        
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("Search:"))
        self.search_edit = QLineEdit()
        self.search_edit.textChanged.connect(self.filter_assets)
        search_layout.addWidget(self.search_edit)
        layout.addLayout(search_layout)
        
        self.asset_list = QListWidget()
        self.asset_list.itemSelectionChanged.connect(self.on_asset_selected)
        self.asset_list.itemDoubleClicked.connect(self.on_asset_double_clicked)
        layout.addWidget(self.asset_list)
        
        info_layout = QVBoxLayout()
        self.asset_info_label = QLabel("No asset selected")
        info_layout.addWidget(self.asset_info_label)
        layout.addLayout(info_layout)
        
        self.setLayout(layout)
    
    def set_engine(self, engine):
        self.engine = engine
        self.refresh_assets()
    
    def refresh_assets(self):
        self.asset_list.clear()
        
        if not self.engine:
            return
        
        asset_manager = self.engine.get_asset_manager()
        assets = asset_manager.get_asset_list()
        
        for asset_name in assets:
            asset = asset_manager.get_asset(asset_name)
            if self.should_show_asset(asset):
                item = QListWidgetItem(asset_name)
                item.setData(Qt.ItemDataRole.UserRole, asset_name)
                
                if asset.type == "texture":
                    item.setIcon(QIcon("icons/texture.png"))
                elif asset.type == "model":
                    item.setIcon(QIcon("icons/model.png"))
                elif asset.type == "sound":
                    item.setIcon(QIcon("icons/sound.png"))
                elif asset.type == "material":
                    item.setIcon(QIcon("icons/material.png"))
                elif asset.type == "shader":
                    item.setIcon(QIcon("icons/shader.png"))
                
                self.asset_list.addItem(item)
    
    def should_show_asset(self, asset):
        if self.current_filter == "All":
            return True
        elif self.current_filter == "Textures" and asset.type == "texture":
            return True
        elif self.current_filter == "Models" and asset.type == "model":
            return True
        elif self.current_filter == "Sounds" and asset.type == "sound":
            return True
        elif self.current_filter == "Materials" and asset.type == "material":
            return True
        elif self.current_filter == "Shaders" and asset.type == "shader":
            return True
        return False
    
    def filter_changed(self, filter_text):
        self.current_filter = filter_text
        self.refresh_assets()
    
    def filter_assets(self, search_text):
        for i in range(self.asset_list.count()):
            item = self.asset_list.item(i)
            if search_text.lower() in item.text().lower():
                item.setHidden(False)
            else:
                item.setHidden(True)
    
    def on_asset_selected(self):
        selected_items = self.asset_list.selectedItems()
        if selected_items:
            item = selected_items[0]
            asset_name = item.data(Qt.ItemDataRole.UserRole)
            
            if self.engine:
                asset_manager = self.engine.get_asset_manager()
                asset = asset_manager.get_asset(asset_name)
                if asset:
                    info_text = f"Name: {asset.name}\n"
                    info_text += f"Type: {asset.type}\n"
                    info_text += f"Path: {asset.file_path}\n"
                    info_text += f"Loaded: {'Yes' if asset.loaded else 'No'}"
                    self.asset_info_label.setText(info_text)
            
            self.asset_selected.emit(asset_name)
        else:
            self.asset_info_label.setText("No asset selected")
    
    def on_asset_double_clicked(self, item):
        asset_name = item.data(Qt.ItemDataRole.UserRole)
        if self.engine:
            asset_manager = self.engine.get_asset_manager()
            asset = asset_manager.get_asset(asset_name)
            if asset and asset.type == "texture":
                self.preview_texture(asset)
    
    def preview_texture(self, asset):
        pass
    
    def import_asset(self):
        if not self.engine:
            QMessageBox.warning(self, "Import Error", "No engine available")
            return
        
        file_path, _ = QFileDialog.getOpenFileName(
            self, 
            "Import Asset",
            "",
            "All Files (*);;Images (*.png *.jpg *.jpeg *.bmp *.tga);;Models (*.obj *.fbx *.dae *.gltf *.glb);;Sounds (*.wav *.mp3 *.ogg)"
        )
        
        if file_path:
            asset_manager = self.engine.get_asset_manager()
            asset_name = os.path.splitext(os.path.basename(file_path))[0]
            
            if asset_manager.import_asset(file_path, asset_name):
                self.refresh_assets()
                QMessageBox.information(self, "Import Success", f"Asset '{asset_name}' imported successfully")
            else:
                QMessageBox.warning(self, "Import Error", f"Failed to import asset from {file_path}")
    
    def create_asset_folders(self):
        if not self.engine:
            return
        
        asset_manager = self.engine.get_asset_manager()
        base_path = asset_manager.default_assets_path
        
        folders = ["textures", "models", "sounds", "materials", "shaders", "scripts"]
        for folder in folders:
            folder_path = os.path.join(base_path, folder)
            os.makedirs(folder_path, exist_ok=True)
