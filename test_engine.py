#!/usr/bin/env python3

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_engine():
    try:
        from engine import GameEngine
        from engine.entity import Entity
        from engine.components import MeshRenderer
        
        print("Testing basic engine initialization...")
        engine = GameEngine(800, 600, "Test Engine")
        
        print("Creating test entity...")
        entity = Entity("Test Entity")
        entity.add_component(MeshRenderer())
        
        scene = engine.get_scene_manager().get_current_scene()
        scene.add_entity(entity)
        
        print("Engine test successful!")
        print(f"Scene has {len(scene.entities)} entities")
        
        engine.shutdown()
        return True
        
    except Exception as e:
        print(f"Engine test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_editor():
    try:
        print("Testing editor components...")
        from editor.project_manager import ProjectManager
        from editor.game_builder import GameBuilder
        
        pm = ProjectManager()
        gb = GameBuilder()
        
        print("Editor components test successful!")
        return True
        
    except Exception as e:
        print(f"Editor test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Running 3D Game Engine Tests...")
    print("=" * 50)
    
    engine_ok = test_basic_engine()
    editor_ok = test_editor()
    
    print("=" * 50)
    if engine_ok and editor_ok:
        print("All tests passed! ✓")
    else:
        print("Some tests failed! ✗")
        sys.exit(1)
