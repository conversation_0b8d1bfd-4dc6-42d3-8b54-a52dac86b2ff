# 3D Game Engine

A comprehensive 3D game engine built in Python with support for:

- 3D rendering with OpenGL
- Physics simulation
- Asset management
- Visual editor
- Multiplayer networking
- Game export and packaging
- Player and server runtime

## Features

### ✅ Completed Features

#### Core Engine
- ✅ 3D rendering pipeline with ModernGL (OpenGL 3.3)
- ✅ Scene management and entity-component system
- ✅ Asset loading and management (textures, models, sounds, materials, shaders)
- ✅ Camera system (FPS and Orbit cameras)
- ✅ Input and time management systems
- 🚧 Physics integration with PyBullet (framework ready)

#### Visual Editor
- ✅ PyQt6-based visual editor with dockable panels
- ✅ Scene hierarchy panel for entity management
- ✅ Inspector panel for real-time property editing
- ✅ Asset browser with importing capabilities
- ✅ Project manager for project creation and configuration
- ✅ Game builder for export functionality
- 🚧 Map/terrain editor (planned)
- 🚧 NPC and entity placement tools (planned)

#### Player System
- ✅ Standalone game player for exported games
- ✅ Basic first-person camera controls
- 🚧 Configurable movement and physics (needs physics integration)
- 🚧 Spawn point management (framework ready)

#### Multiplayer
- ✅ WebSocket-based client-server architecture
- ✅ Dedicated server system
- ✅ Basic player synchronization framework
- 🚧 Real-time entity synchronization (needs completion)

#### Export System
- ✅ Game packaging to standalone zip files
- ✅ Separate server packages for multiplayer
- ✅ Runtime player for exported games
- ✅ Asset bundling and project configuration

## Installation

```bash
pip install -r requirements.txt
```

## Usage

### Running the Editor
```bash
python main.py
```

### Running a Game
```bash
python player/game_player.py <game_package.zip>
```

### Running a Server
```bash
python server/game_server.py <server_package.zip>
```

## Project Structure

```
Game-Engine/
├── engine/                 # Core engine components
│   ├── core.py            # Main GameEngine class
│   ├── scene.py           # Scene and SceneManager
│   ├── entity.py          # Entity-Component System
│   ├── renderer.py        # 3D rendering pipeline
│   ├── camera.py          # Camera implementations
│   ├── components.py      # Game components
│   ├── asset_manager.py   # Asset loading and management
│   ├── input_manager.py   # Input handling
│   └── time_manager.py    # Time and delta time management
├── editor/                # Visual editor
│   ├── main_editor.py     # Main editor window
│   ├── hierarchy.py       # Scene hierarchy panel
│   ├── inspector.py       # Property inspector panel
│   ├── asset_browser.py   # Asset browser panel
│   ├── project_manager.py # Project management
│   └── game_builder.py    # Game export system
├── player/                # Game runtime
│   └── game_player.py     # Standalone game player
├── server/                # Multiplayer server
│   └── game_server.py     # Dedicated game server
├── shaders/               # GLSL shaders
│   ├── basic.vert         # Basic vertex shader
│   └── basic.frag         # Basic fragment shader
├── main.py               # Editor launcher
├── test_engine.py        # Engine testing
└── requirements.txt      # Python dependencies
```

## Architecture

### Entity-Component System
The engine uses a flexible ECS architecture where:
- **Entities** are containers with unique IDs
- **Components** hold data (Transform, MeshRenderer, etc.)
- **Systems** process components (rendering, physics, etc.)

### Rendering Pipeline
- OpenGL 3.3 Core Profile with ModernGL
- Shader-based rendering with materials
- Forward rendering with multiple light support
- Camera-based view and projection matrices

### Components Available
- **Transform**: Position, rotation, scale with hierarchical relationships
- **MeshRenderer**: 3D mesh rendering with materials
- **Light**: Directional, point, and spot lighting
- **Rigidbody**: Physics body integration (ready for PyBullet)
- **Collider**: Collision detection components
- **AudioSource**: 3D spatial audio support
- **Script**: Custom behavior scripting system
