#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from editor.main_editor import GameEditor

def main():
    """Main entry point for the game engine editor."""
    try:
        editor = GameEditor()
        editor.run()
    except KeyboardInterrupt:
        print("\nShutting down game engine...")
    except Exception as e:
        print(f"Error starting game engine: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
