#version 330 core

in vec3 frag_pos;
in vec3 normal;
in vec2 uv;

uniform vec3 camera_pos;
uniform vec4 diffuse_color;
uniform vec3 specular_color;
uniform float shininess;

uniform vec3 light_direction;
uniform vec3 light_color;
uniform vec3 ambient_light;

out vec4 frag_color;

void main() {
    vec3 norm = normalize(normal);
    vec3 light_dir = normalize(-light_direction);
    
    float diff = max(dot(norm, light_dir), 0.0);
    vec3 diffuse = diff * light_color;
    
    vec3 view_dir = normalize(camera_pos - frag_pos);
    vec3 reflect_dir = reflect(-light_dir, norm);
    float spec = pow(max(dot(view_dir, reflect_dir), 0.0), shininess);
    vec3 specular = spec * specular_color * light_color;
    
    vec3 result = (ambient_light + diffuse + specular) * diffuse_color.rgb;
    frag_color = vec4(result, diffuse_color.a);
}
